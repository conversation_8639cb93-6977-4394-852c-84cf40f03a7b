<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>零工市场-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202503281048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

    <style>
        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 零工市场网格容器 */
        .market-slider-container {
            position: relative;
            margin-top: 20px;
            width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .market-slider {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            width: 100%;
            transition: none;
            margin-bottom: 30px;
        }

        .market-slider .place-card-new {
            width: 100%;
            min-width: 0;
            max-width: none;
            overflow: hidden;
        }

        /* 隐藏滑动箭头，因为不再需要滑动功能 */
        .slider-arrow {
            display: none !important;
        }

        /* 场地信息卡片样式 */
        .place-card-new {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e6e6e6;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            height: 320px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }

        .place-card-new:hover {
            border-color: #0052d9;
            box-shadow: 0 4px 16px rgba(0,82,217,0.15);
            transform: translateY(-2px);
        }

        .place-card-new.featured {
            border-color: #28a745;
            background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
        }

        /* 卡片内容布局优化 */
        .card-header-new {
            margin-bottom: 12px;
        }

        .place-meta-tags-new {
            margin-bottom: 12px;
        }

        .info-grid-new {
            flex: 1;
            margin-bottom: 12px;
        }

        .card-footer-new {
            margin-top: auto;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
        }

        .featured-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 3px 6px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: bold;
            z-index: 1;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .card-header-new {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .place-title-new {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
            flex: 1;
            margin-right: 15px;
        }

        .place-rent-new {
            font-size: 16px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 6px 12px;
            border-radius: 6px;
            white-space: nowrap;
        }

        .place-meta-tags-new {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .meta-tag-new {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .type-tag-new {
            background: #e3f2fd;
            color: #1976d2;
        }

        .level-tag-new {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .region-tag-new {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .info-grid-new {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .info-item-new {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon-new {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .info-content-new {
            flex: 1;
        }

        .info-label-new {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .info-value-new {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .card-footer-new {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
            margin-top: auto;
        }

        .place-address-new {
            font-size: 12px;
            color: #999;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .card-footer-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 8px;
        }

        .view-count-new {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .detail-btn-new {
            padding: 6px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .detail-btn-new:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        /* 确保页面底部正确 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .width100 {
            flex: 1;
            min-height: calc(100vh - 200px); /* 确保主内容区域有足够高度 */
        }

        #footerBar {
            margin-top: auto;
            clear: both;
            position: relative;
            z-index: 100; /* 确保底部在最上层 */
        }

        /* 防止推荐标签影响布局 */
        .place-card-new, .employment-card-new {
            position: relative;
            overflow: visible;
        }

        /* 确保卡片容器有足够的底部间距 */
        .market-slider-container, .employment-slider-container {
            padding-bottom: 20px;
        }

        /* 用工信息卡片样式 */
        .employment-card-new {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e6e6e6;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            height: 320px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }

        .employment-card-new:hover {
            border-color: #0052d9;
            box-shadow: 0 4px 16px rgba(0,82,217,0.15);
            transform: translateY(-2px);
        }

        .employment-card-new.featured {
            border-color: #28a745;
            background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
        }

        /* 用工信息卡片头部 */
        .employment-card-header-new {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .employment-title-new {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
            flex: 1;
            margin-right: 15px;
        }

        .employment-salary-new {
            font-size: 16px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 6px 12px;
            border-radius: 6px;
            white-space: nowrap;
        }

        .employment-meta-tags-new {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .employment-type-tag-new {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .employment-category-tag-new {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .employment-region-tag-new {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        /* 用工信息网格 */
        .employment-info-grid-new {
            flex: 1;
            margin-bottom: 12px;
        }

        .employment-info-item-new {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .employment-info-icon-new {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .employment-info-content-new {
            flex: 1;
        }

        .employment-info-label-new {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .employment-info-value-new {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        /* 用工信息卡片底部 */
        .employment-card-footer-new {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
            margin-top: auto;
        }

        .employment-company-new {
            font-size: 12px;
            color: #999;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .employment-card-footer-actions {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 8px;
        }

        .employment-btn-new {
            padding: 6px 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .employment-btn-new:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        /* 用工信息网格容器 */
        .employment-slider-container {
            position: relative;
            margin-top: 20px;
            width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .employment-slider {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            width: 100%;
            transition: none;
            margin-bottom: 30px;
        }

        .employment-slider .employment-card-new {
            width: 100%;
            min-width: 0;
            max-width: none;
            overflow: hidden;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .market-slider {
                grid-template-columns: repeat(3, 1fr);
                gap: 18px;
            }
            .employment-slider {
                grid-template-columns: repeat(3, 1fr);
                gap: 18px;
            }
            .market-slider-container,
            .employment-slider-container {
                padding: 0 12px;
            }
        }

        @media (max-width: 900px) {
            .market-slider {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            .employment-slider {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            .market-slider-container,
            .employment-slider-container {
                padding: 0 10px;
            }
        }

        @media (max-width: 768px) {
            .market-slider {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .employment-slider {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .market-slider-container,
            .employment-slider-container {
                padding: 0 8px;
            }
            .place-card-new,
            .employment-card-new {
                height: auto;
                min-height: 280px;
                padding: 12px;
            }
            .card-footer-new,
            .employment-card-footer-new {
                gap: 6px;
            }
            .card-footer-actions,
            .employment-card-footer-actions {
                gap: 6px;
            }
            .detail-btn-new,
            .employment-btn-new {
                padding: 5px 10px;
                font-size: 11px;
            }
        }
    </style>
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <div class="">
        <!-- banner 开始 -->
        <div class="bannerBox pr none">
            <!-- ko if:bagnnerList().length>0 -->
            <div class="bannerSlide" data-bind="foreach:bagnnerList">
                <img src="./image/index_banner.png" data-bind="attr:{src:fullPath}">
            </div>
            <div class="hd" data-bind="visible:bagnnerList().length>1"><ul></ul></div>
            <!-- /ko -->
            <!-- ko if:bagnnerList().length==0 -->
            <div class="bannerSlide" >
                <img src="./image/index_banner.png" >
            </div>
            <!-- /ko -->
            <div class="conAuto2 pr">
                <!-- slideTxtBox start -->
                <!-- ko if:cdggList().length>0 -->
                
                <!-- /ko -->
                <!-- slideTxtBox end -->

            </div>
        </div>
        <!-- banner end -->

        <!-- bannerNew  -->
         <div class="bannerNew"></div>
        <!-- bannerNew end -->

        <!-- 第一部分 start -->
        <div class="conAuto2 cycdBox">
            <ul class="clearfix numout">
                <li class="fl cp bg1">
                    <div id="onerun01" class="num"></div>
                    <p class="text">零工市场（个）</p>
                </li>
                <li class="fl  bg2">
                    <div id="onerun02" class="num"></div>
                    <p class="text">工人容纳量（人）</p>
                </li>
                <li class="fl  bg3">
                    <div id="onerun03" class="num"></div>
                    <p class="text">当前工人数（人）</p>
                </li>
                <li class="fl  bg4">
                    <div id="onerun04" class="num"></div>
                    <p class="text">日均用工需求（人）</p>
                </li>
            </ul>
        </div>
        <!-- 第三部分 start -->
        <div class="cycdMainBox">
            <div class="conAuto2" >
                <div class="clearfix mb20">
                    <div class="fl">
                        <p class="contentTitle">零工<em>市场</em></p>
                        <p class="xg"></p>
                    </div>
                    <!-- <div class="fr">
                        <div class="search-box" style="display: flex; align-items: center; gap: 10px;">
                            <input type="text" placeholder="搜索零工市场..."
                                   data-bind="value: searchKeyword, valueUpdate: 'afterkeydown'"
                                   style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; width: 200px;"
                                   onkeypress="if(event.keyCode==13) viewModel.searchMarkets()">
                            <button onclick="viewModel.searchMarkets()"
                                    style="padding: 8px 16px; background: #0052d9; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                搜索
                            </button>
                        </div>
                    </div> -->
                </div>
                
                <!-- 场地信息列表 -->
                <div class="placeInfoList" id="placeInfoList">
                    <!-- 加载状态 -->
                    <div id="loadingState" style="display: none; text-align: center; padding: 40px;">
                        <div class="loading-spinner" style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #0052d9; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 15px; color: #666;">正在加载场地信息...</p>
                    </div>

                    <!-- 零工市场网格容器 -->
                    <div id="marketSliderContainer" class="market-slider-container" style="display: none;">
                        <div id="marketSlider" class="market-slider">
                            <!-- 零工市场卡片将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataTip" style="display: none; text-align: center; padding: 80px 20px; background: rgba(255,255,255,0.95); border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); margin: 40px auto; max-width: 400px;">
                        <div style="font-size: 64px; color: #e0e0e0; margin-bottom: 24px; line-height: 1;">🏢</div>
                        <h3 style="color: #666; margin-bottom: 12px; font-size: 20px; font-weight: 500;">暂无场地信息</h3>
                        <p style="color: #999; font-size: 14px; line-height: 1.5; margin: 0;">请调整筛选条件或稍后再试</p>
                    </div>

                    <!-- 原有的knockout绑定列表（保持兼容） -->
                    <ul class="clearfix cycdList" data-bind="foreach:cycdList()">
                        <li class="fl transi pr">
                            <!-- <em class="tabs pa">高能级园区</em> -->
                            <a class=" block" href="javascript:;" target="_blank"
                            data-bind="attr:{href:'./placeDetail.html?id='+baseId}">
                            <p  class="title block textoverflow"
                                data-bind="text:parkName,attr:{title:parkName}"></p>
                                <div class="clearfix">
                                    <div class="fl mainLeft">

                                        <div class="clearfix">
                                            <p class="fl textoverflow bq1" data-bind="text:parkType,visible:parkType"></p>
                                            <p class="fl textoverflow bq2" data-bind="text:parkLevel,visible:parkLevel"></p>
                                        </div>
                                        <p class="liText textoverflow mt10">工人容纳量：<span data-bind="text:acreage+'人'"></span></p>
                                        <p class="liText textoverflow">当前工人数：<span data-bind="text:rzCompanyCount+'人'"></span>
                                        </p>
                                        <p class="liText textoverflow">营业时间：
                                           <!-- ko if:isOpenSettle=='0' -->
                                           <span data-bind="text:applyTimeStatus==0?'全天开放':((applyStartDate?applyStartDate.substring(0,10):'--')+'至'+(applyEndDate?applyEndDate.substring(0,10):'--'))"></span>
                                           <!-- /ko -->
                                           <!-- ko if:isOpenSettle=='1' -->
                                           定期开放，暂未营业
                                           <!-- /ko -->
                                        </p>

                                    </div>
                                    <p  class="fr block imgA transi">
                                        <!-- ko if:imageUrl -->
                                        <img src="" class="mainImg transi" data-bind="attr:{src:imageUrl}">
                                        <!-- /ko -->
                                        <!-- ko if:!imageUrl -->
                                        <img src="../public/images/pics/pic_noList.png" class="mainImg transi">
                                        <!-- /ko -->
                                    </p>
                                </div>

                                <p class="pos textoverflow" data-bind="text:address,attr:{title:address}">
                                </p>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 暂无数据 -->
                <div class="nodataPic none nodataPicCycd"></div>
            </div>
        </div>
        <!-- 第三部分 end -->

        <!-- 用工信息部分 start -->
        <div class="cycdMainBox" >
            <div class="conAuto2">
                <div class="clearfix mb20">
                    <div class="fl">
                        <p class="contentTitle">用工<em>信息</em></p>
                        <p class="xg"></p>
                    </div>
                    <!-- <div class="fr">
                        <div class="search-box" style="display: flex; align-items: center; gap: 10px;">
                            <input type="text" placeholder="搜索用工信息..."
                                   data-bind="value: employmentSearchKeyword, valueUpdate: 'afterkeydown'"
                                   style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; width: 200px;"
                                   onkeypress="if(event.keyCode==13) viewModel.searchEmployments()">
                            <button onclick="viewModel.searchEmployments()"
                                    style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                搜索
                            </button>
                        </div>
                    </div> -->
                </div>


                <!-- 用工信息列表 -->
                <div class="employmentInfoList" id="employmentInfoList">
                    <!-- 加载状态 -->
                    <div id="employmentLoadingState" style="display: none; text-align: center; padding: 40px;">
                        <div class="loading-spinner" style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #0052d9; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 15px; color: #666;">正在加载用工信息...</p>
                    </div>

                    <!-- 用工信息网格容器 -->
                    <div id="employmentSliderContainer" class="employment-slider-container" style="display: none;">
                        <div id="employmentSlider" class="employment-slider">
                            <!-- 用工信息卡片将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 无数据提示 -->
                    <div id="employmentNoDataTip" style="display: none; text-align: center; padding: 80px 20px; background: rgba(255,255,255,0.95); border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); margin: 40px auto; max-width: 400px;">
                        <div style="font-size: 64px; color: #e0e0e0; margin-bottom: 24px; line-height: 1;">💼</div>
                        <h3 style="color: #666; margin-bottom: 12px; font-size: 20px; font-weight: 500;">暂无用工信息</h3>
                        <p style="color: #999; font-size: 14px; line-height: 1.5; margin: 0;">请调整筛选条件或稍后再试</p>
                    </div>
                </div>
                <!-- 暂无数据 -->
                <div class="nodataPic none nodataPicEmployment"></div>
            </div>
        </div>
        <!-- 用工信息部分 end -->

        <!-- 第四部分 start -->
        <!-- <div class="rzlcBox">
            <div class="conAuto2">
                <div>
                    <p class="contentTitle">场地服务商<em>认证流程</em></p>
                    <p class="xg"></p>
                </div>
                <div class="mt50">
                    <div class="clearfix ulDiv">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg1.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">1</p>
                                <p class="f18 text-gray3 mt10 ml10">场地服务商登录</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg2.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">2</p>
                                <p class="f18 text-gray3 mt10 ml10">在线实名认证</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg3.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">3</p>
                                <p class="f18 text-gray3 mt10 ml10">审核</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi mr0">
                            <img src="./image/index4LiImg4.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">4</p>
                                <p class="f18 text-gray3 mt10 ml10">认证成功</p>
                            </div>
                        </div>
                        <a href="javascript:;" class="fl block ml20 animationBtn" onclick="sqrzFun()">
                            <img src="./image/index4Ljrz.png">
                        </a>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
    <!-- main end -->
    <!-- 底部 开始 -->
    <div id="footerBar"> </div>
    <!-- 底部 开始 -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <script src="../public/numberRun/numberRunAll.js" type="text/javascript" charset="utf-8"></script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/index.js?v=202503261407" type="text/javascript" charset="utf-8"></script>






</body>

</html>